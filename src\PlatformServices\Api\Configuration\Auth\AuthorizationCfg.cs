﻿using Api.Configuration.Auth.Handler;
using Api.Extensions;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;

namespace Api.Configuration.Auth
{
    public static class AuthorizationCfg
    {
        public static IServiceCollection AddAuth(
            this IServiceCollection services,
            IConfiguration configuration,
            IWebHostEnvironment environment,
            Action<Options.AuthorizationOptions> options)
        {
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, o =>
            {
                o.Authority = configuration["Auth:BaseUrl"];
                o.SaveToken = false;
                o.RequireHttpsMetadata = false;
                o.IncludeErrorDetails = true;
                o.MetadataAddress = configuration["Auth:BaseUrl"] + configuration["Auth:MetadataEndpoint"];

                o.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
                {
                    ValidAudiences = configuration.GetSection("Auth:Audiences").Get<List<string>>(),
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero,
                    RoleClaimType = "role",
                    NameClaimType = "preferred_username"
                };

                o.Events = new JwtBearerEvents()
                {
                    OnAuthenticationFailed = c =>
                    {
                        c.NoResult();
                        c.Response.StatusCode = 401;
                        c.Response.ContentType = "text/plain";

                        if (environment.IsLocal())
                        {
                            return c.Response.WriteAsync(c.Exception.Message);
                        }

                        return c.Response.WriteAsync("Unauthorized");
                    }
                };
            });

            services.Configure(options);
            services.AddHttpContextAccessor();

            services.AddSingleton<IClaimsTransformation>(new RolesClaimsTransformation());
            services.AddSingleton<IAuthorizationHandler, AuthorizationHandler>();

            return services;
        }
    }
}
