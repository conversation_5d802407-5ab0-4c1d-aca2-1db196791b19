﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;

namespace Api.Configuration.Swagger
{
    public static class SwaggerCfg
    {
        public static IServiceCollection AddSwagger(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "Api",
                    Version = "v1"
                });

                //options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                //{
                //    Name = "Authorization",
                //    In = ParameterLocation.Header,
                //    Type = SecuritySchemeType.OAuth2,
                //    Flows = new OpenApiOAuthFlows
                //    {
                //        Implicit = new OpenApiOAuthFlow
                //        {
                //            Scopes = new Dictionary<string, string>
                //            {
                //                { "openid", "OpenId" },
                //                { "profile", "Profile" }
                //            },
                //            TokenUrl = new Uri($"{configuration["Auth:BaseUrl"]}{configuration["Auth:TokenEndpoint"]}"),
                //            AuthorizationUrl = new Uri($"{configuration["Auth:BaseUrl"]}{configuration["Auth:AuthorizationEndpoint"]}"),
                //        }
                //    }
                //});

                options.EnableAnnotations();
                options.OperationFilter<SecurityRequirementsOperationFilter>();
                options.OperationFilter<JsonIgnoreQueryOperationFilter>();
                options.OperationFilter<RemoveVersionFromParameter>();
                options.DocumentFilter<ReplaceVersionWithExactValueInPath>();
            });

            return services;
        }
    }
}