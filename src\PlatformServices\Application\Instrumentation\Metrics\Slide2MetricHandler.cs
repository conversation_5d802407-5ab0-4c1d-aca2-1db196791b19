using System;
using System.Diagnostics.Metrics;
using static Application.Instrumentation.Metrics.Slide2MetricsConstants;

namespace Application.Instrumentation.Metrics;

public class Slide2MetricHandler
{
    /// <summary>
    /// Number of stability analysis sent to Slide2 executable that successfully completed.
    /// </summary>
    private readonly Counter<int> _slide2AnalysisCompleted;

    /// <summary>
    /// Number of stability analysis sent to Slide2 executable that timed out.
    /// </summary>
    private readonly Counter<int> _slide2AnalysisTimedOut;

    /// <summary>
    /// Number of stability analysis sent to Slide2 executable that failed because of unknown reasons.
    /// </summary>
    private readonly Counter<int> _slide2AnalysisFailed;

    /// <summary>
    /// Duration of stability analysis processed by Slide2 executable.
    /// </summary>
    private readonly Counter<double> _slide2AnalysisDuration;

    public Slide2MetricHandler(IMeterFactory meterFactory)
    {
        var meter = meterFactory.Create(
            ApplicationConstants.ServiceName,
            ApplicationConstants.Version);

        _slide2AnalysisCompleted = meter.CreateCounter<int>(
            Names.AnalysisCompleted,
            MeasurementUnits.Occurrences);

        _slide2AnalysisTimedOut = meter.CreateCounter<int>(
            Names.AnalysisTimedOut,
            MeasurementUnits.Occurrences);

        _slide2AnalysisFailed = meter.CreateCounter<int>(
            Names.AnalysisFailed,
            MeasurementUnits.Occurrences);

        _slide2AnalysisDuration = meter.CreateCounter<double>(
            Names.AnalysisDuration,
            MeasurementUnits.Seconds);
    }
    
    public void IncrementSlide2AnalysisCompleted(int quantity = 1)
    {
        _slide2AnalysisCompleted.Add(quantity);
    }

    public void IncrementSlide2AnalysisTimedOut(int quantity = 1)
    {
        _slide2AnalysisTimedOut.Add(quantity);
    }

    public void IncrementSlide2AnalysisFailed(int quantity = 1)
    {
        _slide2AnalysisFailed.Add(quantity);
    }

    public void IncrementSlide2AnalysisDuration(TimeSpan time)
    {
        _slide2AnalysisDuration.Add(time.TotalSeconds);
    }
}
