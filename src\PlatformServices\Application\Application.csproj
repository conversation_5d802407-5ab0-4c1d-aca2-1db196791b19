﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
	<FrameworkReference Include="Microsoft.AspNetCore.App" />
	<PackageReference Include="Microsoft.Extensions.Diagnostics" Version="8.0.0" />
	<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
	<PackageReference Include="Polly.Core" Version="8.6.2" />
    <PackageReference Include="Refit" Version="6.3.2" />
    <PackageReference Include="Refit.HttpClientFactory" Version="6.3.2" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Application.Core\Application.Core.csproj" />
    <ProjectReference Include="..\Database\Database.csproj" />
  </ItemGroup>
</Project>