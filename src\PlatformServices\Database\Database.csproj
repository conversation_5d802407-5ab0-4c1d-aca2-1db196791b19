﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Database.Core\Database.Core.csproj" />
    <ProjectReference Include="..\Domain\Domain.csproj" />
    <ProjectReference Include="..\Model\Model.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.0.123" />
    <PackageReference Include="Dapper.SqlBuilder" Version="2.0.78" />
    <PackageReference Include="FluentMigrator" Version="3.3.1" />
    <PackageReference Include="FluentMigrator.Runner" Version="3.3.1" />
    <PackageReference Include="FluentMigrator.Runner.SqlServer" Version="3.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Localization.Abstractions" Version="7.0.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Migrations\" />
    <Folder Include="Repositories\" />
  </ItemGroup>
</Project>