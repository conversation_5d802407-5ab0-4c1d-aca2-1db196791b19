﻿using Application.Core;
using Application.Extensions;
using Model.Slide2.CreateS01File.Request;
using System;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<System.Guid>;

namespace Application.Slide2.CreateS01File
{
    public sealed class CreateS01FileUseCase : ICreateS01FileUseCase
    {
        private readonly CreateS01FileRequestValidator _requestValidator = new();
        private readonly S01BackgroundTaskQueue _taskQueue;

        public CreateS01FileUseCase(
            S01BackgroundTaskQueue taskQueue)
        {
            _taskQueue = taskQueue;
        }


        public async Task<UseCaseResponse<Guid>> Execute(CreateS01FileRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(Guid.Empty);
                }

                var validationResult = await _requestValidator
                   .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(Guid.Empty,
                        validationResult.Errors.ToErrorMessages());
                }

                var fileName = Guid.NewGuid();

                _taskQueue.QueueBackgroundWorkItem(fileName, request);

                return Ok(fileName);
            }
            catch (Exception e)
            {
                return InternalServerError(Guid.Empty, errors: e.ToErrorMessages("000"));
            }
        }
    }
}
