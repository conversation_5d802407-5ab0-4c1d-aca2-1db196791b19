﻿
[*.cs]
#### Estilos de nomenclatura ####

# Regras de nomenclatura

dotnet_naming_rule.interface_should_be_begins_with_i.severity = suggestion
dotnet_naming_rule.interface_should_be_begins_with_i.symbols = interface
dotnet_naming_rule.interface_should_be_begins_with_i.style = begins_with_i

dotnet_naming_rule.types_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.types_should_be_pascal_case.symbols = types
dotnet_naming_rule.types_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.non_field_members_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.non_field_members_should_be_pascal_case.symbols = non_field_members
dotnet_naming_rule.non_field_members_should_be_pascal_case.style = pascal_case

# Especificações de símbolo

dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.interface.required_modifiers = 

dotnet_naming_symbols.types.applicable_kinds = class, struct, interface, enum
dotnet_naming_symbols.types.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.types.required_modifiers = 

dotnet_naming_symbols.non_field_members.applicable_kinds = property, event, method
dotnet_naming_symbols.non_field_members.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.non_field_members.required_modifiers = 

# Estilos de nomenclatura

dotnet_naming_style.begins_with_i.required_prefix = I
dotnet_naming_style.begins_with_i.required_suffix = 
dotnet_naming_style.begins_with_i.word_separator = 
dotnet_naming_style.begins_with_i.capitalization = pascal_case

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case
csharp_style_expression_bodied_methods = false:silent
csharp_style_expression_bodied_constructors = false:silent
csharp_style_expression_bodied_operators = false:silent
csharp_style_expression_bodied_properties = true:silent
csharp_style_expression_bodied_indexers = true:silent
csharp_style_expression_bodied_accessors = true:silent
csharp_style_expression_bodied_lambdas = true:silent
csharp_style_expression_bodied_local_functions = false:silent
csharp_using_directive_placement = outside_namespace:silent
csharp_style_prefer_switch_expression = true:suggestion
csharp_style_prefer_pattern_matching = true:silent
csharp_space_around_binary_operators = before_and_after

[*.vb]
#### Estilos de nomenclatura ####

# Regras de nomenclatura

dotnet_naming_rule.interface_should_be_começa_com_i.severity = suggestion
dotnet_naming_rule.interface_should_be_começa_com_i.symbols = interface
dotnet_naming_rule.interface_should_be_começa_com_i.style = começa_com_i

dotnet_naming_rule.tipos_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.tipos_should_be_pascal_case.symbols = tipos
dotnet_naming_rule.tipos_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.membros_sem_campo_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.membros_sem_campo_should_be_pascal_case.symbols = membros_sem_campo
dotnet_naming_rule.membros_sem_campo_should_be_pascal_case.style = pascal_case

# Especificações de símbolo

dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, friend, private, protected, protected_friend, private_protected
dotnet_naming_symbols.interface.required_modifiers = 

dotnet_naming_symbols.tipos.applicable_kinds = class, struct, interface, enum
dotnet_naming_symbols.tipos.applicable_accessibilities = public, friend, private, protected, protected_friend, private_protected
dotnet_naming_symbols.tipos.required_modifiers = 

dotnet_naming_symbols.membros_sem_campo.applicable_kinds = property, event, method
dotnet_naming_symbols.membros_sem_campo.applicable_accessibilities = public, friend, private, protected, protected_friend, private_protected
dotnet_naming_symbols.membros_sem_campo.required_modifiers = 

# Estilos de nomenclatura

dotnet_naming_style.começa_com_i.required_prefix = I
dotnet_naming_style.começa_com_i.required_suffix = 
dotnet_naming_style.começa_com_i.word_separator = 
dotnet_naming_style.começa_com_i.capitalization = pascal_case

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case

[*.{cs,vb}]
dotnet_style_qualification_for_field = false:silent
dotnet_style_qualification_for_property = false:silent
dotnet_style_qualification_for_method = false:silent
dotnet_style_qualification_for_event = false:silent
dotnet_style_readonly_field = true:suggestion
end_of_line = crlf
dotnet_style_operator_placement_when_wrapping = beginning_of_line