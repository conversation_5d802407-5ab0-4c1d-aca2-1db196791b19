﻿using Application.Core;
using Domain.Core;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Linq;
using System.Threading;

namespace Api.Extensions
{
    public static class UseCaseResponseExtensions
    {
        public static ObjectResult ToObjectResult<T>(this UseCaseResponse<T> response)
        {
            return response.IsSuccessful
                ? new ObjectResult(response.Result)
                {
                    StatusCode = (int)response.Status
                }
                : new ObjectResult(response.Errors)
                {
                    StatusCode = (int)response.Status
                };
        }

        public static ObjectResult ToObjectResult<T>(
            this UseCaseResponse<T> response,
            IStringLocalizer localizer,
            Locale locale = Locale.En)
        {
            return response.IsSuccessful
                ? new ObjectResult(response.Result)
                {
                    StatusCode = (int)response.Status
                }
                : new ObjectResult(response.Errors.Select(x =>
                {
                    Thread.CurrentThread.ChangeCulture(locale);

                    var resource = localizer.GetString(x.Code);

                    if (resource.ResourceNotFound)
                    {
                        return new ErrorMessage(x.Code, x.Message);
                    }

                    return new ErrorMessage(x.Code, string.Format(resource.Value, x.Message));
                }))
                {
                    StatusCode = (int)response.Status
                };
        }
    }
}