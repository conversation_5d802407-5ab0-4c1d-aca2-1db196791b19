﻿using Domain.Core;
using System;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Security.Claims;

namespace Api.Extensions
{
    public static class ClaimsPrincipalExtensions
    {
        public static Guid Id(this ClaimsPrincipal claims)
        {
            var id = claims.FindFirst(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
            return !string.IsNullOrWhiteSpace(id) ? Guid.Parse(id) : Guid.Empty;
        }

        public static Locale Locale(this ClaimsPrincipal claims)
        {
            var role = claims.Claims
                .First(x => GetEnumValueFromDescription<Locale>(x.Value) != default).Value;
            return GetEnumValueFromDescription<Locale>(role);
        }

        private static T GetEnumValueFromDescription<T>(string description)
        {
            MemberInfo[] fis = typeof(T).GetFields();

            foreach (var fi in fis)
            {
                var attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

                if (attributes != null && attributes.Length > 0 && attributes[0].Description == description)
                    return (T)Enum.Parse(typeof(T), fi.Name);
            }

            return default;
        }
    }
}
