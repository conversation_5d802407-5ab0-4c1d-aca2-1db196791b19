﻿using ClosedXML.Excel;
using ClosedXML.Graphics;
using SixLabors.Fonts;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;

namespace Application.Core
{
    public static class GraphicsEngine
    {
        /// <summary>
        /// If runtime platform is not Windows, we have to set graphics engine fallback font in order
        /// for AdjustToContents to work.
        /// We're setting preferred font to DejaVu Sans, which is available on most linux distros. If not, we're
        /// falling back to first available font.
        /// </summary>
        public static void UpdateGraphicsEngineFonts()
        {
            if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                const string preferredFont = "DejaVu Sans";
                List<string> fonts = SystemFonts.Families.Select(f => f.Name).ToList();

                // If the preferred font is not available, use the first available font
                var fontName = fonts.Contains(preferredFont) ? preferredFont : fonts.First();

                // All workbooks created later will use the engine with a fallback font DejaVu Sans
                // (or the first available font)
                LoadOptions.DefaultGraphicEngine = new DefaultGraphicEngine(fontName);
            }
        }
    }
}
