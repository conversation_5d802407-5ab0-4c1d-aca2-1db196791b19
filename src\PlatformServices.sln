﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31919.166
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Api", "PlatformServices\Api\Api.csproj", "{197366C2-86DD-4BD1-9486-91D6DA2ADB6F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application", "PlatformServices\Application\Application.csproj", "{2968099B-C39C-411F-BE3C-17B23452262C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Database", "PlatformServices\Database\Database.csproj", "{98622902-63B2-4789-9072-2D399183139C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Domain", "PlatformServices\Domain\Domain.csproj", "{29070D21-C804-40C2-BA8A-E6EACC57FFDE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Model", "PlatformServices\Model\Model.csproj", "{F87CA5D9-751E-4F81-BBF1-8EC6C005BC6A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Domain.Core", "PlatformServices\Domain.Core\Domain.Core.csproj", "{9D19F65C-8A31-47A8-8CB5-62C8C18F33F1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Database.Core", "PlatformServices\Database.Core\Database.Core.csproj", "{D1D31B34-1128-4031-B295-C090F3BB1051}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application.Core", "PlatformServices\Application.Core\Application.Core.csproj", "{F0FE4692-5396-4817-BA67-480146123B54}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{7FEBA342-00F8-42D7-9CFA-32C9CD8BB948}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application.Tests", "PlatformServices\Application.Tests\Application.Tests.csproj", "{8AEBF6F5-80BD-46D8-91D3-F83A0BED5769}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{FBBBE51C-095D-415B-B3A6-91ACB39E7D79}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Model.Core", "PlatformServices\Model.Core\Model.Core.csproj", "{C6C3A487-5F93-4542-9201-C2C0C92BE705}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{197366C2-86DD-4BD1-9486-91D6DA2ADB6F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{197366C2-86DD-4BD1-9486-91D6DA2ADB6F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{197366C2-86DD-4BD1-9486-91D6DA2ADB6F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{197366C2-86DD-4BD1-9486-91D6DA2ADB6F}.Release|Any CPU.Build.0 = Release|Any CPU
		{2968099B-C39C-411F-BE3C-17B23452262C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2968099B-C39C-411F-BE3C-17B23452262C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2968099B-C39C-411F-BE3C-17B23452262C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2968099B-C39C-411F-BE3C-17B23452262C}.Release|Any CPU.Build.0 = Release|Any CPU
		{98622902-63B2-4789-9072-2D399183139C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98622902-63B2-4789-9072-2D399183139C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98622902-63B2-4789-9072-2D399183139C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{98622902-63B2-4789-9072-2D399183139C}.Release|Any CPU.Build.0 = Release|Any CPU
		{29070D21-C804-40C2-BA8A-E6EACC57FFDE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29070D21-C804-40C2-BA8A-E6EACC57FFDE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29070D21-C804-40C2-BA8A-E6EACC57FFDE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29070D21-C804-40C2-BA8A-E6EACC57FFDE}.Release|Any CPU.Build.0 = Release|Any CPU
		{F87CA5D9-751E-4F81-BBF1-8EC6C005BC6A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F87CA5D9-751E-4F81-BBF1-8EC6C005BC6A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F87CA5D9-751E-4F81-BBF1-8EC6C005BC6A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F87CA5D9-751E-4F81-BBF1-8EC6C005BC6A}.Release|Any CPU.Build.0 = Release|Any CPU
		{9D19F65C-8A31-47A8-8CB5-62C8C18F33F1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9D19F65C-8A31-47A8-8CB5-62C8C18F33F1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9D19F65C-8A31-47A8-8CB5-62C8C18F33F1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9D19F65C-8A31-47A8-8CB5-62C8C18F33F1}.Release|Any CPU.Build.0 = Release|Any CPU
		{D1D31B34-1128-4031-B295-C090F3BB1051}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1D31B34-1128-4031-B295-C090F3BB1051}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1D31B34-1128-4031-B295-C090F3BB1051}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1D31B34-1128-4031-B295-C090F3BB1051}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0FE4692-5396-4817-BA67-480146123B54}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0FE4692-5396-4817-BA67-480146123B54}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0FE4692-5396-4817-BA67-480146123B54}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0FE4692-5396-4817-BA67-480146123B54}.Release|Any CPU.Build.0 = Release|Any CPU
		{8AEBF6F5-80BD-46D8-91D3-F83A0BED5769}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8AEBF6F5-80BD-46D8-91D3-F83A0BED5769}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8AEBF6F5-80BD-46D8-91D3-F83A0BED5769}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8AEBF6F5-80BD-46D8-91D3-F83A0BED5769}.Release|Any CPU.Build.0 = Release|Any CPU
		{C6C3A487-5F93-4542-9201-C2C0C92BE705}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C6C3A487-5F93-4542-9201-C2C0C92BE705}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C6C3A487-5F93-4542-9201-C2C0C92BE705}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C6C3A487-5F93-4542-9201-C2C0C92BE705}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{9D19F65C-8A31-47A8-8CB5-62C8C18F33F1} = {7FEBA342-00F8-42D7-9CFA-32C9CD8BB948}
		{D1D31B34-1128-4031-B295-C090F3BB1051} = {7FEBA342-00F8-42D7-9CFA-32C9CD8BB948}
		{F0FE4692-5396-4817-BA67-480146123B54} = {7FEBA342-00F8-42D7-9CFA-32C9CD8BB948}
		{8AEBF6F5-80BD-46D8-91D3-F83A0BED5769} = {FBBBE51C-095D-415B-B3A6-91ACB39E7D79}
		{C6C3A487-5F93-4542-9201-C2C0C92BE705} = {7FEBA342-00F8-42D7-9CFA-32C9CD8BB948}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {EEB12CA6-4604-44E9-B26F-D4C846480412}
	EndGlobalSection
EndGlobal
