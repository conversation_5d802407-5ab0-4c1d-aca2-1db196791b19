{"Kestrel": {"Endpoints": {"Http": {"Url": "http://0.0.0.0:51234"}}}, "Apis": {"Keycloak": {"Url": "https://logisoil.eastus2.cloudapp.azure.com/auth", "Realm": "logisoil"}}, "AzureMonitor": {"ConnectionString": "InstrumentationKey=b26e5dac-5e4f-480d-840e-5b0ed57c6898;IngestionEndpoint=https://westus2-1.in.applicationinsights.azure.com/;LiveEndpoint=https://westus2.livediagnostics.monitor.azure.com/;ApplicationId=26774bf7-3738-4039-a2d8-2786764c9d92", "SamplingRatio": 0.5, "EnableLiveMetrics": false, "DisableOfflineStorage": true}, "Slide2": {"SlidePath": "C:\\Program Files\\Rocscience\\Slide2\\slide.exe", "ASlidewPath": "C:\\Program Files\\Rocscience\\Slide2\\aslidew.exe", "ExecutionTimeoutInMinutes": 15}, "Auth": {"Clients": {"ClientSecret": "********************************", "ClientId": "logisoil-clients-api"}, "BaseUrl": "https://logisoil.eastus2.cloudapp.azure.com/auth/realms/logisoil", "AuthorizationEndpoint": "/protocol/openid-connect/auth", "MetadataEndpoint": "/.well-known/openid-configuration", "TokenEndpoint": "/protocol/openid-connect/token", "Audiences": ["logisoil-workflows-api", "logisoil-clients-api"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Swagger": {"BaseUrl": "https://localhost:51232/swagger", "OAuth2RedirectEndpoint": "/oauth2-redirect.html"}}