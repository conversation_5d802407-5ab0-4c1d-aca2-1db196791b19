﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<IsPackable>false</IsPackable>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Resources\**" />
	  <EmbeddedResource Remove="Resources\**" />
	  <None Remove="Resources\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="FluentAssertions" Version="6.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.0.0" />
		<PackageReference Include="Moq" Version="4.16.1" />
		<PackageReference Include="xunit" Version="2.4.1" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.4.3">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="3.1.0">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Application.Core\Application.Core.csproj" />
		<ProjectReference Include="..\Application\Application.csproj" />
	</ItemGroup>

</Project>