﻿using Application.Core;
using Microsoft.AspNetCore.Hosting;
using Model.Slide2.GetS01File.Response;
using System;
using System.IO;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<Model.Slide2.GetS01File.Response.GetS01FileResponse>;

namespace Application.Slide2.GetS01File
{
    public sealed class GetS01FileUseCase : IGetS01FileUseCase
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        public GetS01FileUseCase(IWebHostEnvironment app)
        {
            _webHostEnvironment = app;
        }

        public async Task<UseCaseResponse<GetS01FileResponse>> Execute(Guid request)
        {
            try
            {
                if (request == Guid.Empty)
                {
                    return BadRequest(null, "000", "Request cannot be empty");
                }

                if (!File.Exists(@$"{_webHostEnvironment.WebRootPath}\{request}.s01"))
                {
                    return NoContent();
                }

                var fileInfo = new FileInfo(@$"{_webHostEnvironment.WebRootPath}\{request}.s01");

                if (IsFileInUse(fileInfo))
                {
                    return NoContent();
                }

                var base64 = Convert.ToBase64String(File.ReadAllBytes(@$"{_webHostEnvironment.WebRootPath}\{request}.s01"));

                var response = new GetS01FileResponse
                {
                    S01Base64 = base64
                };

                File.Delete(@$"{_webHostEnvironment.WebRootPath}\{request}.s01");

                return Ok(response);
            }   
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }

        private static bool IsFileInUse(FileInfo file)
        {
            try
            {
                using var stream = file.Open(FileMode.Open, FileAccess.Read, FileShare.None);
            }
            catch (IOException)
            {
                return true;
            }

            return false;
        }
    }
}
